"""Database-related test fixtures."""

import uuid
from typing import Any
from unittest.mock import Mock

import pytest
from llama_index.core.llms import ChatMessage

from common.services import JobStore, PostgresChatStore, AgentStore, PromptStore
from common.services.job_store import BatchJobStatusEnum


# Note: Mock fixtures were removed as the tests now use direct mocking
# This keeps the fixtures file clean and focused on data fixtures


# Sample data fixtures
@pytest.fixture
def test_uuids() -> dict[str, uuid.UUID]:
    """Provide consistent UUIDs for testing."""
    return {
        "client_1": uuid.UUID("12345678-1234-5678-9abc-123456789abc"),
        "client_2": uuid.UUID("*************-8765-cba9-987654321cba"),
        "user_1": uuid.UUID("11111111-**************-************"),
        "user_2": uuid.UUID("*************-8888-9999-aaaaaaaaaaaa"),
        "agent_1": uuid.UUID("aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"),
        "job_1": uuid.UUID("ffffffff-0000-1111-2222-************"),
    }


@pytest.fixture
def sample_agent_data() -> dict[str, Any]:
    """Provide sample agent data for testing."""
    return {
        "name": "test_agent",
        "definition": {
            "type": "workflow",
            "steps": [
                {"action": "analyze", "parameters": {"depth": "deep"}},
                {"action": "generate", "parameters": {"format": "json"}},
            ],
            "metadata": {"version": "1.0", "author": "test_user"},
        },
    }


@pytest.fixture
def sample_chat_messages() -> list[ChatMessage]:
    """Provide sample chat messages for testing."""
    return [
        ChatMessage(role="user", content="Hello, how are you?"),
        ChatMessage(role="assistant", content="I'm doing well, thank you! How can I help you today?"),
        ChatMessage(role="user", content="Can you help me with a coding problem?"),
        ChatMessage(role="assistant", content="Of course! I'd be happy to help with your coding problem."),
    ]


@pytest.fixture
def sample_job_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample job data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "total_count": 10,
        "job_params": {
            "model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000,
            "batch_size": 5,
        },
        "status": BatchJobStatusEnum.PENDING,
    }


@pytest.fixture
def sample_prompt_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample prompt data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "prompt": "You are a helpful AI assistant. Please respond to user queries with accurate and helpful information.",
    }
