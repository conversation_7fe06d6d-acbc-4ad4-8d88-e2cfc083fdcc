"""Concise tests for common.models.tags module."""

import uuid
from datetime import date

import pytest
from pydantic import ValidationError

from common.models.tags import (
    LiteLLMDailyTagSpend,
    TagUsageSummary,
    TagUsageDetail,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageResponse
)


class TestLiteLLMDailyTagSpend:
    """Test cases for LiteLLMDailyTagSpend SQLAlchemy model."""

    def test_model_attributes(self):
        """Test model has required attributes and table configuration."""
        model = LiteLLMDailyTagSpend

        # Test table configuration
        assert model.__tablename__ == "LiteLLM_DailyTagSpend"
        assert model.__table_args__["schema"] == "public"

        # Test actual columns exist (based on the source code)
        actual_columns = ["date", "tag", "spend", "api_requests", "prompt_tokens", "completion_tokens"]
        for column in actual_columns:
            assert hasattr(model, column)


class TestTagUsageSummary:
    """Test cases for TagUsageSummary model."""

    def test_creation(self):
        """Test creating TagUsageSummary with valid data."""
        summary = TagUsageSummary(
            tag="production-api",
            total_spend=1250.75,
            total_requests=5000,
            total_tokens=250000,
            success_rate=99.8,
            date_range="2023-10-01 to 2023-10-07"
        )

        assert summary.tag == "production-api"
        assert summary.total_spend == 1250.75
        assert summary.total_requests == 5000
        assert summary.total_tokens == 250000
        assert summary.success_rate == 99.8
        assert summary.date_range == "2023-10-01 to 2023-10-07"

    def test_missing_required_fields(self):
        """Test TagUsageSummary validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            TagUsageSummary(total_spend=100.0)  # Missing tag

        assert "tag" in str(exc_info.value)

    def test_field_types(self):
        """Test TagUsageSummary field type validation."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.50,
            total_requests=1000,
            total_tokens=50000,
            success_rate=95.5,
            date_range="2023-10-01 to 2023-10-07"
        )

        assert isinstance(summary.tag, str)
        assert isinstance(summary.total_spend, float)
        assert isinstance(summary.total_requests, int)
        assert isinstance(summary.total_tokens, int)
        assert isinstance(summary.success_rate, float)
        assert isinstance(summary.date_range, str)


class TestTagUsageDetail:
    """Test cases for TagUsageDetail model."""

    def test_creation(self):
        """Test creating TagUsageDetail with valid data."""
        from datetime import datetime
        detail = TagUsageDetail(
            id=uuid.uuid4(),
            tag="test-tag",
            date="2023-10-01",
            api_key="test-key",
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=100,
            completion_tokens=50,
            cache_read_input_tokens=0,
            cache_creation_input_tokens=0,
            spend=0.05,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        assert isinstance(detail.id, uuid.UUID)
        assert detail.tag == "test-tag"
        assert detail.model == "gpt-4"
        assert detail.spend == 0.05
        assert detail.prompt_tokens == 100
        assert detail.completion_tokens == 50

    def test_json_serialization(self):
        """Test TagUsageDetail JSON serialization."""
        from datetime import datetime
        detail = TagUsageDetail(
            id=uuid.uuid4(),
            tag="test-tag",
            date="2023-10-01",
            api_key="test-key",
            model="gpt-3.5-turbo",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=80,
            completion_tokens=20,
            cache_read_input_tokens=0,
            cache_creation_input_tokens=0,
            spend=0.02,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        json_data = detail.model_dump()
        assert "id" in json_data
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["spend"] == 0.02
        assert json_data["prompt_tokens"] == 80


class TestTagUsageByDate:
    """Test cases for TagUsageByDate model."""

    def test_creation(self):
        """Test creating TagUsageByDate with valid data."""
        usage = TagUsageByDate(
            date="2023-10-01",
            spend=125.50,
            api_requests=500,
            total_tokens=25000,
            success_rate=99.2
        )

        assert usage.date == "2023-10-01"
        assert usage.spend == 125.50
        assert usage.api_requests == 500
        assert usage.total_tokens == 25000
        assert usage.success_rate == 99.2


class TestTagUsageByModel:
    """Test cases for TagUsageByModel model."""

    def test_creation(self):
        """Test creating TagUsageByModel with valid data."""
        usage = TagUsageByModel(
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=875.52,
            total_requests=3500,
            total_tokens=175000,
            success_rate=99.9
        )

        assert usage.model == "gpt-4"
        assert usage.model_group == "openai"
        assert usage.custom_llm_provider == "openai"
        assert usage.total_spend == 875.52
        assert usage.total_requests == 3500
        assert usage.total_tokens == 175000
        assert usage.success_rate == 99.9


class TestTagUsageResponse:
    """Test cases for TagUsageResponse model."""

    def test_creation(self):
        """Test creating TagUsageResponse with all components."""
        summary = TagUsageSummary(
            tag="production-api",
            total_spend=1250.75,
            total_requests=5000,
            total_tokens=250000,
            success_rate=99.8,
            date_range="2023-10-01 to 2023-10-07"
        )

        daily_data = [
            TagUsageByDate(
                date="2023-10-01",
                spend=178.68,
                api_requests=714,
                total_tokens=35714,
                success_rate=99.7
            )
        ]

        model_breakdown = [
            TagUsageByModel(
                model="gpt-4",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=875.52,
                total_requests=3500,
                total_tokens=175000,
                success_rate=99.9
            )
        ]

        response = TagUsageResponse(
            tag="production-api",
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=5000
        )

        assert response.tag == "production-api"
        assert response.summary == summary
        assert len(response.daily_data) == 1
        assert len(response.model_breakdown) == 1
        assert response.total_records == 5000

    def test_json_serialization(self):
        """Test TagUsageResponse JSON serialization."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=100,
            total_tokens=10000,
            success_rate=99.0,
            date_range="2023-10-01"
        )

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0
        )

        json_data = response.model_dump()
        assert json_data["tag"] == "test-tag"
        assert "summary" in json_data
        assert json_data["daily_data"] == []
        assert json_data["model_breakdown"] == []
        assert json_data["total_records"] == 0


class TestTagsModelsIntegration:
    """Integration tests for tag models."""

    def test_model_serialization_consistency(self):
        """Test that all models serialize consistently."""
        summary = TagUsageSummary(
            tag="integration-test",
            total_spend=500.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=98.5,
            date_range="2023-10-01"
        )

        daily = TagUsageByDate(
            date="2023-10-01",
            spend=500.0,
            api_requests=1000,
            total_tokens=50000,
            success_rate=98.5
        )

        model = TagUsageByModel(
            model="gpt-3.5-turbo",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=500.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=98.5
        )

        # All models should serialize without errors
        summary_json = summary.model_dump()
        daily_json = daily.model_dump()
        model_json = model.model_dump()

        assert all(isinstance(data, dict) for data in [summary_json, daily_json, model_json])
        # Check that spend-related fields exist (different field names in different models)
        assert "total_spend" in summary_json
        assert "spend" in daily_json
        assert "total_spend" in model_json
