"""Tests for JobStore service."""

import uuid
from typing import Any
from unittest.mock import Mock, patch

import pytest

from common.services import JobStore
from common.services.job_store import Batch<PERSON>obStatusEnum, Job


class TestJobStoreCreation:
    """Test JobStore creation and initialization."""

    def test_from_params_creation(self):
        """Test JobStore creation from parameters."""
        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_jobs",
                schema_name="custom_schema",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "custom_jobs"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test JobStore creation from URI."""
        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_jobs",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "uri_jobs"
            assert store.schema_name == "public"

    @patch('common.services.job_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.get_instance()
            assert store.table_name == "batch_jobs"


class TestJobStoreOperations:
    """Test JobStore CRUD operations."""

    def test_create_job_sync(self, test_uuids: dict[str, uuid.UUID], sample_job_data: dict[str, Any]):
        """Test synchronous job creation."""
        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock(spec=JobStore)
            mock_job_id = uuid.uuid4()
            store.create_job.return_value = mock_job_id
            mock_new.return_value = store

            job_id = store.create_job(
                client_guid=sample_job_data["client_guid"],
                total_count=sample_job_data["total_count"],
                job_params=sample_job_data["job_params"]
            )

            assert job_id == mock_job_id
            store.create_job.assert_called_once_with(
                client_guid=sample_job_data["client_guid"],
                total_count=sample_job_data["total_count"],
                job_params=sample_job_data["job_params"]
            )

    @pytest.mark.asyncio
    async def test_create_job_async(self, test_uuids: dict[str, uuid.UUID], sample_job_data: dict[str, Any]):
        """Test asynchronous job creation."""
        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock(spec=JobStore)
            mock_job_id = uuid.uuid4()
            store.async_create_job.return_value = mock_job_id
            mock_new.return_value = store

            job_id = await store.async_create_job(
                client_guid=sample_job_data["client_guid"],
                total_count=sample_job_data["total_count"],
                job_params=sample_job_data["job_params"]
            )

            assert job_id == mock_job_id

    def test_get_job_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test synchronous job retrieval."""
        job_id = test_uuids["job_1"]

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock(spec=JobStore)
            mock_job = Mock(spec=Job)
            mock_job.id = job_id
            mock_job.status = BatchJobStatusEnum.PENDING
            store.get_job.return_value = mock_job
            mock_new.return_value = store

            job = store.get_job(job_id)

            assert job.id == job_id
            assert job.status == BatchJobStatusEnum.PENDING

    def test_get_nonexistent_job_sync(self):
        """Test retrieving non-existent job synchronously."""
        fake_id = uuid.uuid4()

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock(spec=JobStore)
            store.get_job.side_effect = LookupError("Job not found")
            mock_new.return_value = store

            with pytest.raises(LookupError, match=r"Job not found"):
                store.get_job(fake_id)


class TestJobStoreStatusManagement:
    """Test job status management."""

    def test_job_status_enum_values(self):
        """Test that job status enum has expected values."""
        # Test that all expected status values exist
        assert BatchJobStatusEnum.PENDING is not None
        assert BatchJobStatusEnum.PROCESSING is not None
        assert BatchJobStatusEnum.COMPLETED is not None
        assert BatchJobStatusEnum.FAILED is not None
        assert BatchJobStatusEnum.PARTIALLY_COMPLETED is not None

    def test_update_job_status_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test synchronous job status update."""
        job_id = test_uuids["job_1"]

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock()
            store.update_job_status = Mock()
            mock_new.return_value = store

            store.update_job_status(job_id, BatchJobStatusEnum.PROCESSING)

            store.update_job_status.assert_called_once_with(job_id, BatchJobStatusEnum.PROCESSING)

    def test_job_progress_tracking(self, test_uuids: dict[str, uuid.UUID]):
        """Test job progress tracking functionality."""
        job_id = test_uuids["job_1"]

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock()
            store.update_job_progress = Mock()
            mock_new.return_value = store

            # Test updating progress
            store.update_job_progress(job_id, completed_count=5, total_count=10)

            store.update_job_progress.assert_called_once_with(
                job_id, completed_count=5, total_count=10
            )


class TestJobStoreDataIntegrity:
    """Test data integrity and validation."""

    def test_complex_job_parameters(self, test_uuids: dict[str, uuid.UUID]):
        """Test storing complex job parameters."""
        complex_params = {
            "model_config": {
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 1000,
                "system_prompt": "You are a helpful assistant"
            },
            "batch_config": {
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300
            },
            "output_config": {
                "format": "json",
                "include_metadata": True,
                "compression": "gzip"
            }
        }

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock(spec=JobStore)
            mock_job_id = uuid.uuid4()
            store.create_job.return_value = mock_job_id
            mock_new.return_value = store

            job_id = store.create_job(
                client_guid=test_uuids["client_1"],
                total_count=100,
                job_params=complex_params
            )

            assert job_id == mock_job_id
            store.create_job.assert_called_once_with(
                client_guid=test_uuids["client_1"],
                total_count=100,
                job_params=complex_params
            )

    def test_client_isolation(self, test_uuids: dict[str, uuid.UUID]):
        """Test that jobs are properly isolated by client."""
        client1_guid = test_uuids["client_1"]
        client2_guid = test_uuids["client_2"]

        with patch.object(JobStore, '__new__') as mock_new:
            store = Mock()

            # Mock different responses for different clients
            def mock_get_jobs_by_client(client_guid):
                if client_guid == client1_guid:
                    return [Mock(id=uuid.uuid4(), client_guid=client1_guid)]
                elif client_guid == client2_guid:
                    return [Mock(id=uuid.uuid4(), client_guid=client2_guid)]
                else:
                    return []

            store.get_jobs_by_client = Mock(side_effect=mock_get_jobs_by_client)
            mock_new.return_value = store

            # Test isolation
            client1_jobs = store.get_jobs_by_client(client1_guid)
            client2_jobs = store.get_jobs_by_client(client2_guid)

            assert len(client1_jobs) == 1
            assert len(client2_jobs) == 1
            assert client1_jobs[0].client_guid == client1_guid
            assert client2_jobs[0].client_guid == client2_guid
