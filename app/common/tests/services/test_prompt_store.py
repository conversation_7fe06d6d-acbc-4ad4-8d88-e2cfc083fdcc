"""Tests for PromptStore service."""

import uuid
from typing import Any
from unittest.mock import Mock, patch

import pytest

from common.services import PromptStore
from common.services.prompt_store import PromptEntry


class TestPromptStoreCreation:
    """Test PromptStore creation and initialization."""

    def test_from_params_creation(self):
        """Test PromptStore creation from parameters."""
        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_prompts",
                schema_name="custom_schema",
                debug=True
            )

            assert store.table_name == "custom_prompts"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test PromptStore creation from URI."""
        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_prompts",
                debug=True
            )

            assert store.table_name == "uri_prompts"
            assert store.schema_name == "public"

    @patch('common.services.prompt_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.get_instance()
            assert store.table_name == "prompts"


class TestPromptStoreOperations:
    """Test PromptStore CRUD operations."""

    def test_create_prompt_sync(self, test_uuids: dict[str, uuid.UUID], sample_prompt_data: dict[str, Any]):
        """Test synchronous prompt creation."""
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompt_id = 1
            store.create_prompt.return_value = mock_prompt_id
            mock_new.return_value = store
            
            prompt_id = store.create_prompt(
                client_guid=sample_prompt_data["client_guid"],
                prompt=sample_prompt_data["prompt"]
            )
            
            assert prompt_id == mock_prompt_id
            store.create_prompt.assert_called_once_with(
                client_guid=sample_prompt_data["client_guid"],
                prompt=sample_prompt_data["prompt"]
            )

    @pytest.mark.asyncio
    async def test_create_prompt_async(self, test_uuids: dict[str, uuid.UUID], sample_prompt_data: dict[str, Any]):
        """Test asynchronous prompt creation."""
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompt_id = 1
            store.async_create_prompt.return_value = mock_prompt_id
            mock_new.return_value = store
            
            prompt_id = await store.async_create_prompt(
                client_guid=sample_prompt_data["client_guid"],
                prompt=sample_prompt_data["prompt"]
            )
            
            assert prompt_id == mock_prompt_id

    def test_get_prompt_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test synchronous prompt retrieval."""
        prompt_id = 1
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompt = Mock(spec=PromptEntry)
            mock_prompt.id = prompt_id
            mock_prompt.prompt = "Test prompt"
            store.get_prompt.return_value = mock_prompt
            mock_new.return_value = store
            
            prompt = store.get_prompt(prompt_id)
            
            assert prompt.id == prompt_id
            assert prompt.prompt == "Test prompt"

    def test_get_nonexistent_prompt_sync(self):
        """Test retrieving non-existent prompt synchronously."""
        fake_id = 999
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            store.get_prompt.side_effect = LookupError("Prompt not found")
            mock_new.return_value = store
            
            with pytest.raises(LookupError, match=r"Prompt not found"):
                store.get_prompt(fake_id)

    def test_list_prompts_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test listing prompts for a client."""
        client_guid = test_uuids["client_1"]
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompts = [
                Mock(id=1, prompt="Prompt 1", client_guid=client_guid),
                Mock(id=2, prompt="Prompt 2", client_guid=client_guid)
            ]
            store.list_prompts.return_value = mock_prompts
            mock_new.return_value = store
            
            prompts = store.list_prompts(client_guid)
            
            assert len(prompts) == 2
            assert all(p.client_guid == client_guid for p in prompts)


class TestPromptStoreUpdateDelete:
    """Test prompt update and delete operations."""

    def test_update_prompt_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test synchronous prompt update."""
        prompt_id = 1
        new_prompt_text = "Updated prompt text"
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_new.return_value = store
            
            store.update_prompt(prompt_id, new_prompt_text)
            
            store.update_prompt.assert_called_once_with(prompt_id, new_prompt_text)

    def test_delete_prompt_sync(self, test_uuids: dict[str, uuid.UUID]):
        """Test synchronous prompt deletion."""
        prompt_id = 1
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_new.return_value = store
            
            store.delete_prompt(prompt_id)
            
            store.delete_prompt.assert_called_once_with(prompt_id)

    def test_delete_nonexistent_prompt_sync(self):
        """Test deleting non-existent prompt synchronously."""
        fake_id = 999
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            store.delete_prompt.side_effect = LookupError("Prompt not found")
            mock_new.return_value = store
            
            with pytest.raises(LookupError, match=r"Prompt not found"):
                store.delete_prompt(fake_id)


class TestPromptStoreClientScoping:
    """Test client scoping and isolation."""

    def test_client_isolation(self, test_uuids: dict[str, uuid.UUID]):
        """Test that prompts are properly isolated by client."""
        client1_guid = test_uuids["client_1"]
        client2_guid = test_uuids["client_2"]
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            
            # Mock different responses for different clients
            def mock_list_prompts(client_guid):
                if client_guid == client1_guid:
                    return [Mock(id=1, client_guid=client1_guid, prompt="Client 1 prompt")]
                elif client_guid == client2_guid:
                    return [Mock(id=2, client_guid=client2_guid, prompt="Client 2 prompt")]
                else:
                    return []
            
            store.list_prompts.side_effect = mock_list_prompts
            mock_new.return_value = store
            
            # Test isolation
            client1_prompts = store.list_prompts(client1_guid)
            client2_prompts = store.list_prompts(client2_guid)
            
            assert len(client1_prompts) == 1
            assert len(client2_prompts) == 1
            assert client1_prompts[0].client_guid == client1_guid
            assert client2_prompts[0].client_guid == client2_guid

    def test_empty_client_prompt_list(self, test_uuids: dict[str, uuid.UUID]):
        """Test handling of clients with no prompts."""
        client_guid = test_uuids["client_1"]
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            store.list_prompts.return_value = []
            mock_new.return_value = store
            
            prompts = store.list_prompts(client_guid)
            
            assert prompts == []
            assert len(prompts) == 0


class TestPromptStoreDataIntegrity:
    """Test data integrity and special cases."""

    def test_unicode_content_handling(self, test_uuids: dict[str, uuid.UUID]):
        """Test handling of unicode and special characters in prompts."""
        client_guid = test_uuids["client_1"]
        unicode_prompt = "Hello 🌟 测试 émojis and spëcial chars: !@#$%^&*()"
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompt = Mock()
            mock_prompt.prompt = unicode_prompt
            store.create_prompt.return_value = 1
            store.get_prompt.return_value = mock_prompt
            mock_new.return_value = store
            
            prompt_id = store.create_prompt(client_guid, unicode_prompt)
            retrieved_prompt = store.get_prompt(prompt_id)
            
            assert retrieved_prompt.prompt == unicode_prompt

    def test_large_prompt_content(self, test_uuids: dict[str, uuid.UUID]):
        """Test handling of large prompt content."""
        client_guid = test_uuids["client_1"]
        large_prompt = "This is a very long prompt. " * 1000  # ~28KB
        
        with patch.object(PromptStore, '__new__') as mock_new:
            store = Mock(spec=PromptStore)
            mock_prompt = Mock()
            mock_prompt.prompt = large_prompt
            store.create_prompt.return_value = 1
            store.get_prompt.return_value = mock_prompt
            mock_new.return_value = store
            
            prompt_id = store.create_prompt(client_guid, large_prompt)
            retrieved_prompt = store.get_prompt(prompt_id)
            
            assert retrieved_prompt.prompt == large_prompt
            assert len(retrieved_prompt.prompt) > 25000  # Verify it's actually large
