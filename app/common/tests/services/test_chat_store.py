"""Tests for PostgresChatStore service."""

import uuid
from typing import Any
from unittest.mock import Mock, patch

import pytest
from llama_index.core.llms import ChatMessage

from common.services import PostgresChatStore


class TestChatStoreCreation:
    """Test PostgresChatStore creation and initialization."""

    def test_from_params_creation(self):
        """Test ChatStore creation from parameters."""
        with patch('common.services.chat_store.create_engine'), \
                patch('common.services.chat_store.create_async_engine'), \
                patch('common.services.chat_store.sessionmaker'):

            store = PostgresChatStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_chatstore",
                schema_name="custom_schema",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "custom_chatstore"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test ChatStore creation from URI."""
        with patch('common.services.chat_store.create_engine'), \
                patch('common.services.chat_store.create_async_engine'), \
                patch('common.services.chat_store.sessionmaker'):

            store = PostgresChatStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_chatstore",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "uri_chatstore"
            assert store.schema_name == "public"


class TestChatStoreOperations:
    """Test chat message operations."""

    def test_add_message_sync(self, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test adding a single message synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = sample_chat_messages[0]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            mock_new.return_value = store
            
            store.add_message(client_guid, user_guid, message)
            
            store.add_message.assert_called_once_with(client_guid, user_guid, message)

    @pytest.mark.asyncio
    async def test_add_message_async(self, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test adding a single message asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = sample_chat_messages[1]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            mock_new.return_value = store
            
            await store.aadd_message(client_guid, user_guid, message)
            
            store.aadd_message.assert_called_once_with(client_guid, user_guid, message)

    def test_get_messages_sync(self, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test retrieving messages synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            store.get_messages.return_value = sample_chat_messages
            mock_new.return_value = store
            
            messages = store.get_messages(client_guid, user_guid)
            
            assert len(messages) == len(sample_chat_messages)
            assert messages == sample_chat_messages

    def test_set_messages_sync(self, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test setting messages (replace all) synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            mock_new.return_value = store
            
            store.set_messages(client_guid, user_guid, sample_chat_messages)
            
            store.set_messages.assert_called_once_with(client_guid, user_guid, sample_chat_messages)


class TestChatStoreClientIsolation:
    """Test client and user isolation."""

    def test_client_user_isolation(self, test_uuids: dict[str, uuid.UUID]):
        """Test that messages are properly isolated by client and user."""
        client1_guid = test_uuids["client_1"]
        client2_guid = test_uuids["client_2"]
        user1_guid = test_uuids["user_1"]
        user2_guid = test_uuids["user_2"]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            
            # Mock different responses for different client/user combinations
            def mock_get_messages(client_guid, user_guid):
                if client_guid == client1_guid and user_guid == user1_guid:
                    return [ChatMessage(role="user", content="Client 1 User 1 message")]
                elif client_guid == client2_guid and user_guid == user2_guid:
                    return [ChatMessage(role="user", content="Client 2 User 2 message")]
                else:
                    return []
            
            store.get_messages.side_effect = mock_get_messages
            mock_new.return_value = store
            
            # Test isolation
            messages_c1u1 = store.get_messages(client1_guid, user1_guid)
            messages_c2u2 = store.get_messages(client2_guid, user2_guid)
            messages_c1u2 = store.get_messages(client1_guid, user2_guid)
            
            assert len(messages_c1u1) == 1
            assert len(messages_c2u2) == 1
            assert len(messages_c1u2) == 0
            assert messages_c1u1[0].content == "Client 1 User 1 message"
            assert messages_c2u2[0].content == "Client 2 User 2 message"


class TestChatStoreDataIntegrity:
    """Test data integrity and special cases."""

    def test_unicode_content_handling(self, test_uuids: dict[str, uuid.UUID]):
        """Test handling of unicode and special characters in messages."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        
        unicode_message = ChatMessage(
            role="user", 
            content="Hello 🌟 测试 émojis and spëcial chars: !@#$%^&*()"
        )

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            store.get_messages.return_value = [unicode_message]
            mock_new.return_value = store
            
            store.add_message(client_guid, user_guid, unicode_message)
            messages = store.get_messages(client_guid, user_guid)
            
            assert len(messages) == 1
            assert messages[0].content == unicode_message.content

    def test_empty_conversation_handling(self, test_uuids: dict[str, uuid.UUID]):
        """Test handling of empty conversations."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        with patch.object(PostgresChatStore, '__new__') as mock_new:
            store = Mock(spec=PostgresChatStore)
            store.get_messages.return_value = []
            mock_new.return_value = store
            
            messages = store.get_messages(client_guid, user_guid)
            
            assert messages == []
            assert len(messages) == 0
