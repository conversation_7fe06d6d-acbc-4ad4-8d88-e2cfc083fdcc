"""Tests for AgentStore service."""

import uuid
from typing import Any
from unittest.mock import Mock, patch

import pytest
from sqlalchemy.exc import OperationalError

from common.services import AgentStore
from common.services.agent_store import AgentEntry


class TestAgentStoreCreation:
    """Test AgentStore creation and initialization."""

    def test_from_params_creation(self):
        """Test AgentStore creation from parameters."""
        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_agents",
                schema_name="custom_schema",
                debug=True
            )

            assert store.table_name == "custom_agents"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test AgentStore creation from URI."""
        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_agents",
                debug=True
            )

            assert store.table_name == "uri_agents"
            assert store.schema_name == "public"

    @patch('common.services.agent_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.get_instance()
            assert store.table_name == "agents"


class TestAgentStoreOperations:
    """Test AgentStore CRUD operations."""

    def test_create_agent_sync(self, sample_agent_data: dict[str, Any]):
        """Test synchronous agent creation."""
        # Mock the database operations
        mock_session = Mock()
        mock_table_entry = Mock()
        mock_table_entry.id = uuid.uuid4()

        with patch.object(AgentStore, '__new__') as mock_new:
            # Create a mock store instance
            store = Mock(spec=AgentStore)
            store.create_agent.return_value = mock_table_entry.id
            mock_new.return_value = store

            agent_id = store.create_agent(
                name=sample_agent_data["name"],
                definition=sample_agent_data["definition"]
            )

            assert isinstance(agent_id, uuid.UUID)
            store.create_agent.assert_called_once_with(
                name=sample_agent_data["name"],
                definition=sample_agent_data["definition"]
            )

    @pytest.mark.asyncio
    async def test_create_agent_async(self, sample_agent_data: dict[str, Any]):
        """Test asynchronous agent creation."""
        mock_agent_id = uuid.uuid4()

        with patch.object(AgentStore, '__new__') as mock_new:
            store = Mock(spec=AgentStore)
            store.async_create_agent.return_value = mock_agent_id
            mock_new.return_value = store

            agent_id = await store.async_create_agent(
                name=sample_agent_data["name"],
                definition=sample_agent_data["definition"]
            )

            assert agent_id == mock_agent_id
            store.async_create_agent.assert_called_once_with(
                name=sample_agent_data["name"],
                definition=sample_agent_data["definition"]
            )

    def test_get_nonexistent_agent_sync(self):
        """Test retrieving non-existent agent synchronously."""
        fake_id = uuid.uuid4()

        with patch.object(AgentStore, '__new__') as mock_new:
            store = Mock(spec=AgentStore)
            store.get_agent.side_effect = LookupError("Agent not found")
            mock_new.return_value = store

            with pytest.raises(LookupError, match=r"Agent not found"):
                store.get_agent(fake_id)


class TestAgentStoreDataIntegrity:
    """Test data integrity and validation."""

    def test_complex_definition_storage(self):
        """Test storing complex JSON definitions."""
        complex_definition: dict[str, Any] = {
            "workflow": {
                "steps": [
                    {
                        "id": "step1",
                        "type": "llm_call",
                        "config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 1000,
                            "system_prompt": "You are a helpful assistant"
                        },
                        "inputs": ["user_query"],
                        "outputs": ["response"]
                    }
                ],
                "connections": [
                    {"from": "step1", "to": "step2", "condition": "success"}
                ]
            },
            "metadata": {
                "version": "2.1.0",
                "author": "test_user",
                "tags": ["production", "validated"]
            }
        }

        with patch.object(AgentStore, '__new__') as mock_new:
            store = Mock(spec=AgentStore)
            mock_agent_id = uuid.uuid4()
            mock_agent = Mock()
            mock_agent.definition = complex_definition

            store.create_agent.return_value = mock_agent_id
            store.get_agent.return_value = mock_agent
            mock_new.return_value = store

            agent_id = store.create_agent(
                name="complex_agent",
                definition=complex_definition
            )

            retrieved_agent = store.get_agent(agent_id)
            assert retrieved_agent.definition == complex_definition


class TestAgentStoreErrorHandling:
    """Test error handling and edge cases."""

    def test_connection_error_handling(self):
        """Test handling of database connection errors."""
        with patch('common.services.agent_store.create_engine') as mock_engine:
            mock_engine.side_effect = OperationalError("Connection failed", {}, None)

            with pytest.raises(OperationalError):
                AgentStore.from_params(
                    host="invalid_host",
                    port="5432",
                    database="test_db",
                    user="test_user",
                    password="test_pass"
                )

    def test_empty_definition_handling(self):
        """Test handling of empty or minimal definitions."""
        with patch.object(AgentStore, '__new__') as mock_new:
            store = Mock(spec=AgentStore)
            mock_agent_id = uuid.uuid4()
            mock_agent = Mock()
            mock_agent.definition = {}

            store.create_agent.return_value = mock_agent_id
            store.get_agent.return_value = mock_agent
            mock_new.return_value = store

            # Test with empty dict
            agent_id = store.create_agent(
                name="empty_definition_agent",
                definition={}
            )

            agent = store.get_agent(agent_id)
            assert agent.definition == {}
